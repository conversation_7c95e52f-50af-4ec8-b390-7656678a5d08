// pages/material/stocktaking/task/list.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../../utils/permission.js'
import { StocktakingAPI } from '../../../../utils/stocktaking-api.js'

Page({
  data: {
    // 搜索条件
    searchKeyword: '',
    
    // 当前标签页
    activeTab: 'all',
    
    // 任务列表
    taskList: [],
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 10,
      total: 0,
      pages: 0
    },
    
    // 加载状态
    loading: false,
    finished: false,
    refreshing: false,
    
    // 状态映射
    statusMap: {
      'pending': { label: '待执行', color: '#ff976a', type: 'warning' },
      'running': { label: '执行中', color: '#1989fa', type: 'primary' },
      'completed': { label: '已完成', color: '#07c160', type: 'success' },
      'paused': { label: '已暂停', color: '#969799', type: 'default' }
    }
  },

  onLoad() {
    console.log('📋 我的盘点任务页面加载')
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.onRefresh()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('📋 初始化我的盘点任务页面')
    this.loadTaskList()
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      // 重置分页信息
      this.setData({
        'pageInfo.current': 1,
        taskList: [],
        finished: false
      })
      
      // 重新加载数据
      await this.loadTaskList(true)
      
      Toast.success('数据已更新')
    } catch (error) {
      console.error('刷新数据失败:', error)
      Toast.fail('刷新失败')
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true })
    
    this.refreshData().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (this.data.loading || this.data.finished) {
      return
    }
    
    // 加载下一页数据
    this.setData({
      'pageInfo.current': this.data.pageInfo.current + 1
    })
    
    this.loadTaskList()
  },

  /**
   * 加载任务列表
   */
  async loadTaskList(isRefresh = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size
      }

      // 添加状态筛选
      if (this.data.activeTab !== 'all') {
        params.status = this.data.activeTab
      }

      // 添加搜索条件
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        const keyword = this.data.searchKeyword.trim()
        // 根据关键词特征判断搜索类型
        if (/^[A-Z0-9]+$/i.test(keyword)) {
          // 如果是字母数字组合，按任务编号搜索
          params.taskCode = keyword
        } else {
          // 否则按任务名称搜索
          params.taskName = keyword
        }
      }

      console.log('📋 盘点任务查询参数:', params)

      // 调用真实API
      const response = await StocktakingAPI.getMyTasks(params)
      
      if (response && response.code === 200 && response.data) {
        const { records, total, size, current, pages } = response.data
        
        console.log('📋 获取到盘点任务列表:', records)
        
        // 更新列表数据
        let newList = []
        if (isRefresh || this.data.pageInfo.current === 1) {
          // 刷新或首次加载，替换数据
          newList = records || []
        } else {
          // 加载更多，追加数据
          newList = [...this.data.taskList, ...(records || [])]
        }
        
        // 更新页面数据
        this.setData({
          taskList: newList,
          'pageInfo.total': total || 0,
          'pageInfo.size': size || 10,
          'pageInfo.current': current || 1,
          'pageInfo.pages': pages || 0,
          finished: (current >= pages) || (records && records.length === 0)
        })
        
        console.log('✅ 盘点任务列表加载完成，共', newList.length, '条记录')
      } else {
        throw new Error(response?.msg || '获取盘点任务列表失败')
      }
    } catch (error) {
      console.error('❌ 加载盘点任务列表失败:', error)
      
      // 如果是首次加载失败，显示错误信息
      if (this.data.pageInfo.current === 1) {
        Toast.fail('获取数据失败')
      }
      
      // 恢复页码
      if (this.data.pageInfo.current > 1) {
        this.setData({
          'pageInfo.current': this.data.pageInfo.current - 1
        })
      }
    } finally {
      this.setData({ loading: false })
    }
  },



  /**
   * 标签页切换
   */
  onTabChange(event) {
    const activeTab = event.detail.name
    console.log('切换标签页:', activeTab)
    
    this.setData({ activeTab })
    
    // 重置分页并重新加载数据
    this.setData({
      'pageInfo.current': 1,
      taskList: [],
      finished: false
    })
    
    this.loadTaskList(true)
  },

  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const value = event.detail
    this.setData({
      searchKeyword: value
    })
    
    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
    
    this.searchTimer = setTimeout(() => {
      if (value !== this.data.searchKeyword) return
      this.performSearch(value)
    }, 500)
  },

  /**
   * 搜索确认
   */
  onSearch(event) {
    const keyword = event.detail || this.data.searchKeyword
    this.performSearch(keyword)
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    console.log('🔍 执行盘点任务搜索，关键词:', keyword)
    
    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      taskList: [],
      finished: false
    })
    
    // 重新加载数据
    this.loadTaskList(true)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshData()
  },

  /**
   * 任务项点击
   */
  onTaskItemClick(event) {
    const task = event.currentTarget.dataset.task
    console.log('点击任务项:', task)
    
    // 跳转到任务详情页面
    this.onViewTaskDetail(event)
  },

  /**
   * 查看任务详情
   */
  onViewTaskDetail(event) {
    const task = event.currentTarget.dataset.task
    console.log('查看任务详情:', task)
    
    wx.navigateTo({
      url: `/pages/material/stocktaking/task/detail?taskId=${task.taskId}`,
      fail: () => {
        Toast.fail('任务详情功能开发中')
      }
    })
  },

  /**
   * 开始任务
   */
  onStartTask(event) {
    event.stopPropagation() // 阻止事件冒泡
    const task = event.currentTarget.dataset.task
    console.log('开始任务:', task)
    
    // 跳转到扫码盘点页面
    wx.navigateTo({
      url: `/pages/material/stocktaking/scan?taskId=${task.taskId}`,
      fail: () => {
        Toast.fail('扫码盘点功能开发中')
      }
    })
  },

  /**
   * 继续任务
   */
  onContinueTask(event) {
    event.stopPropagation() // 阻止事件冒泡
    const task = event.currentTarget.dataset.task
    console.log('继续任务:', task)
    
    // 跳转到扫码盘点页面
    wx.navigateTo({
      url: `/pages/material/stocktaking/scan?taskId=${task.taskId}`,
      fail: () => {
        Toast.fail('扫码盘点功能开发中')
      }
    })
  },

  /**
   * 查看结果
   */
  onViewResult(event) {
    event.stopPropagation() // 阻止事件冒泡
    const task = event.currentTarget.dataset.task
    console.log('查看结果:', task)
    
    // 跳转到盘点结果页面
    wx.navigateTo({
      url: `/pages/material/stocktaking/task/detail?taskId=${task.taskId}&tab=result`,
      fail: () => {
        Toast.fail('盘点结果功能开发中')
      }
    })
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})
