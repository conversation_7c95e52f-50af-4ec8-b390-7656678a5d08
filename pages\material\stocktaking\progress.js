// pages/material/stocktaking/progress.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'

Page({
  data: {
    // 任务ID
    taskId: '',
    
    // 任务信息
    taskInfo: {},
    
    // 当前标签页
    activeTab: 'personal',
    
    // 个人统计
    personalStats: {},
    
    // 个人记录
    personalRecords: [],
    
    // 团队排行榜
    teamRanking: [],
    
    // 部门统计
    deptStats: [],
    
    // 分类统计
    categoryStats: []
  },

  onLoad(options) {
    console.log('📊 盘点进度页面加载', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取任务ID
    const { taskId } = options
    if (!taskId) {
      Toast.fail('任务ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ taskId })

    // 加载数据
    this.loadAllData()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.taskId) {
      this.loadAllData()
    }
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadAllData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    try {
      await Promise.all([
        this.loadTaskInfo(),
        this.loadPersonalData(),
        this.loadTeamData(),
        this.loadCategoryData()
      ])
    } catch (error) {
      console.error('❌ 加载数据失败:', error)
      Toast.fail('加载数据失败')
    }
  },

  /**
   * 加载任务信息
   */
  async loadTaskInfo() {
    try {
      // 模拟API调用
      const response = await this.mockGetTaskProgress(this.data.taskId)
      
      if (response && response.code === 200 && response.data) {
        this.setData({
          taskInfo: response.data
        })
      } else {
        throw new Error(response?.msg || '获取任务信息失败')
      }
    } catch (error) {
      console.error('❌ 加载任务信息失败:', error)
      throw error
    }
  },

  /**
   * 模拟获取任务进度API
   */
  async mockGetTaskProgress(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟数据
    const mockTask = {
      taskId: taskId,
      taskCode: 'T2025010001',
      taskName: '办公区域盘点任务',
      totalCount: 150,
      completedCount: 75,
      remainingCount: 75,
      differenceCount: 8,
      progressPercent: 50
    }

    return {
      code: 200,
      msg: '操作成功',
      data: mockTask
    }
  },

  /**
   * 加载个人数据
   */
  async loadPersonalData() {
    try {
      // 加载个人统计
      const statsResponse = await this.mockGetPersonalStats(this.data.taskId)
      if (statsResponse && statsResponse.code === 200) {
        this.setData({
          personalStats: statsResponse.data
        })
      }

      // 加载个人记录
      const recordsResponse = await this.mockGetPersonalRecords(this.data.taskId)
      if (recordsResponse && recordsResponse.code === 200) {
        this.setData({
          personalRecords: recordsResponse.data
        })
      }
    } catch (error) {
      console.error('❌ 加载个人数据失败:', error)
      throw error
    }
  },

  /**
   * 模拟获取个人统计API
   */
  async mockGetPersonalStats(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 模拟数据
    const mockStats = {
      todayCount: 25,
      todayDifference: 3,
      avgTime: '2.5分钟',
      ranking: '第3名'
    }

    return {
      code: 200,
      msg: '操作成功',
      data: mockStats
    }
  },

  /**
   * 模拟获取个人记录API
   */
  async mockGetPersonalRecords(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 模拟数据
    const mockRecords = [
      {
        recordId: 'record001',
        assetCode: 'A2025010001',
        assetName: '办公桌',
        hasDifference: false,
        recordTime: '2025-01-16 14:30'
      },
      {
        recordId: 'record002',
        assetCode: 'A2025010002',
        assetName: '办公椅',
        hasDifference: true,
        recordTime: '2025-01-16 14:25'
      },
      {
        recordId: 'record003',
        assetCode: 'A2025010003',
        assetName: '电脑显示器',
        hasDifference: false,
        recordTime: '2025-01-16 14:20'
      }
    ]

    return {
      code: 200,
      msg: '操作成功',
      data: mockRecords
    }
  },

  /**
   * 加载团队数据
   */
  async loadTeamData() {
    try {
      // 加载团队排行榜
      const rankingResponse = await this.mockGetTeamRanking(this.data.taskId)
      if (rankingResponse && rankingResponse.code === 200) {
        this.setData({
          teamRanking: rankingResponse.data
        })
      }

      // 加载部门统计
      const deptResponse = await this.mockGetDeptStats(this.data.taskId)
      if (deptResponse && deptResponse.code === 200) {
        this.setData({
          deptStats: deptResponse.data
        })
      }
    } catch (error) {
      console.error('❌ 加载团队数据失败:', error)
      throw error
    }
  },

  /**
   * 模拟获取团队排行榜API
   */
  async mockGetTeamRanking(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))

    // 模拟数据
    const mockRanking = [
      {
        userId: 'user001',
        userName: '张三',
        departmentName: '行政部',
        completedCount: 45
      },
      {
        userId: 'user002',
        userName: '李四',
        departmentName: '财务部',
        completedCount: 38
      },
      {
        userId: 'user003',
        userName: '王五',
        departmentName: '技术部',
        completedCount: 32
      },
      {
        userId: 'user004',
        userName: '赵六',
        departmentName: '行政部',
        completedCount: 28
      }
    ]

    return {
      code: 200,
      msg: '操作成功',
      data: mockRanking
    }
  },

  /**
   * 模拟获取部门统计API
   */
  async mockGetDeptStats(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))

    // 模拟数据
    const mockDeptStats = [
      {
        deptId: 'dept001',
        departmentName: '行政部',
        totalCount: 60,
        completedCount: 45,
        progressPercent: 75,
        peopleCount: 3
      },
      {
        deptId: 'dept002',
        departmentName: '财务部',
        totalCount: 40,
        completedCount: 25,
        progressPercent: 63,
        peopleCount: 2
      },
      {
        deptId: 'dept003',
        departmentName: '技术部',
        totalCount: 50,
        completedCount: 20,
        progressPercent: 40,
        peopleCount: 2
      }
    ]

    return {
      code: 200,
      msg: '操作成功',
      data: mockDeptStats
    }
  },

  /**
   * 加载分类数据
   */
  async loadCategoryData() {
    try {
      // 加载分类统计
      const categoryResponse = await this.mockGetCategoryStats(this.data.taskId)
      if (categoryResponse && categoryResponse.code === 200) {
        this.setData({
          categoryStats: categoryResponse.data
        })
      }
    } catch (error) {
      console.error('❌ 加载分类数据失败:', error)
      throw error
    }
  },

  /**
   * 模拟获取分类统计API
   */
  async mockGetCategoryStats(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))

    // 模拟数据
    const mockCategoryStats = [
      {
        categoryId: 'cat001',
        categoryName: '办公家具',
        totalCount: 80,
        completedCount: 60,
        differenceCount: 5,
        progressPercent: 75
      },
      {
        categoryId: 'cat002',
        categoryName: '电子设备',
        totalCount: 45,
        completedCount: 20,
        differenceCount: 2,
        progressPercent: 44
      },
      {
        categoryId: 'cat003',
        categoryName: '办公用品',
        totalCount: 25,
        completedCount: 15,
        differenceCount: 1,
        progressPercent: 60
      }
    ]

    return {
      code: 200,
      msg: '操作成功',
      data: mockCategoryStats
    }
  },

  /**
   * 标签页切换
   */
  onTabChange(event) {
    const activeTab = event.detail.name
    console.log('切换标签页:', activeTab)

    this.setData({ activeTab })
  }
})
