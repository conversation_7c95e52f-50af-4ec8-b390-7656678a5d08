// utils/stocktaking-api.js
// 资产盘点相关API接口封装

import { request } from './request.js'

/**
 * 资产盘点API服务
 */
export class StocktakingAPI {
  
  // ==================== 任务管理相关 ====================
  
  /**
   * 获取我的盘点任务列表
   * @param {Object} params 查询参数
   * @param {number} params.pageNum 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.status 任务状态
   * @param {string} params.taskName 任务名称
   * @param {string} params.taskCode 任务编号
   * @returns {Promise}
   */
  static getMyTasks(params = {}) {
    return request({
      url: '/asset/stocktaking/task/my-tasks',
      method: 'GET',
      data: params
    })
  }

  /**
   * 获取任务详情
   * @param {string} taskId 任务ID
   * @returns {Promise}
   */
  static getTaskDetail(taskId) {
    return request({
      url: `/asset/stocktaking/task/${taskId}`,
      method: 'GET'
    })
  }

  /**
   * 开始执行任务
   * @param {string} taskId 任务ID
   * @returns {Promise}
   */
  static startTask(taskId) {
    return request({
      url: `/asset/stocktaking/task/start/${taskId}`,
      method: 'PUT'
    })
  }

  /**
   * 获取任务资产列表
   * @param {string} taskId 任务ID
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getTaskAssets(taskId, params = {}) {
    return request({
      url: `/asset/stocktaking/task/${taskId}/assets`,
      method: 'GET',
      data: params
    })
  }

  // ==================== 盘点执行相关 ====================

  /**
   * 根据资产编码获取资产信息
   * @param {string} assetCode 资产编码
   * @returns {Promise}
   */
  static getAssetInfo(assetCode) {
    return request({
      url: `/asset/info/${assetCode}`,
      method: 'GET'
    })
  }

  /**
   * 创建盘点记录
   * @param {Object} recordData 盘点记录数据
   * @returns {Promise}
   */
  static createRecord(recordData) {
    return request({
      url: '/asset/stocktaking/record',
      method: 'POST',
      data: recordData
    })
  }

  /**
   * 更新盘点记录
   * @param {string} recordId 记录ID
   * @param {Object} recordData 更新数据
   * @returns {Promise}
   */
  static updateRecord(recordId, recordData) {
    return request({
      url: `/asset/stocktaking/record/${recordId}`,
      method: 'PUT',
      data: recordData
    })
  }

  /**
   * 获取盘点记录列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getRecordList(params = {}) {
    return request({
      url: '/asset/stocktaking/record/list',
      method: 'GET',
      data: params
    })
  }

  // ==================== 差异处理相关 ====================

  /**
   * 创建差异记录
   * @param {Object} differenceData 差异数据
   * @returns {Promise}
   */
  static createDifference(differenceData) {
    return request({
      url: '/asset/stocktaking/difference',
      method: 'POST',
      data: differenceData
    })
  }

  /**
   * 更新差异处理
   * @param {string} diffId 差异ID
   * @param {Object} updateData 更新数据
   * @returns {Promise}
   */
  static updateDifference(diffId, updateData) {
    return request({
      url: `/asset/stocktaking/difference/${diffId}`,
      method: 'PUT',
      data: updateData
    })
  }

  // ==================== 文件上传相关 ====================

  /**
   * 上传照片
   * @param {string} filePath 文件路径
   * @param {Object} formData 表单数据
   * @returns {Promise}
   */
  static uploadPhoto(filePath, formData = {}) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token')
      
      wx.uploadFile({
        url: `${request.baseURL}/common/upload/photo`,
        filePath: filePath,
        name: 'file',
        formData: formData,
        header: {
          'Authorization': token ? `Bearer ${token}` : ''
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 200) {
              resolve(data)
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (error) {
            reject(new Error('响应数据解析失败'))
          }
        },
        fail: reject
      })
    })
  }

  /**
   * 上传语音文件
   * @param {string} filePath 文件路径
   * @param {Object} formData 表单数据
   * @returns {Promise}
   */
  static uploadAudio(filePath, formData = {}) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token')
      
      wx.uploadFile({
        url: `${request.baseURL}/common/upload/audio`,
        filePath: filePath,
        name: 'file',
        formData: formData,
        header: {
          'Authorization': token ? `Bearer ${token}` : ''
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 200) {
              resolve(data)
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (error) {
            reject(new Error('响应数据解析失败'))
          }
        },
        fail: reject
      })
    })
  }

  /**
   * 关联照片到盘点记录
   * @param {string} recordId 记录ID
   * @param {Array} photoUrls 照片URL数组
   * @returns {Promise}
   */
  static associatePhotos(recordId, photoUrls) {
    return request({
      url: `/asset/stocktaking/record/${recordId}/photos`,
      method: 'PUT',
      data: { photoUrls }
    })
  }

  // ==================== 进度查询相关 ====================

  /**
   * 获取个人进度
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getMyProgress(params = {}) {
    return request({
      url: '/asset/stocktaking/task/my-progress',
      method: 'GET',
      data: params
    })
  }

  /**
   * 获取个人统计
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getMyStatistics(params = {}) {
    return request({
      url: '/asset/stocktaking/record/my-statistics',
      method: 'GET',
      data: params
    })
  }

  /**
   * 获取团队进度
   * @param {string} planId 计划ID
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getTeamProgress(planId, params = {}) {
    return request({
      url: `/asset/stocktaking/plan/${planId}/team-progress`,
      method: 'GET',
      data: params
    })
  }

  /**
   * 获取团队统计
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getTeamStatistics(params = {}) {
    return request({
      url: '/asset/stocktaking/task/team-statistics',
      method: 'GET',
      data: params
    })
  }

  // ==================== 历史记录相关 ====================

  /**
   * 获取历史记录
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getMyHistory(params = {}) {
    return request({
      url: '/asset/stocktaking/record/my-history',
      method: 'GET',
      data: params
    })
  }

  /**
   * 获取记录详情
   * @param {string} recordId 记录ID
   * @returns {Promise}
   */
  static getRecordDetails(recordId) {
    return request({
      url: `/asset/stocktaking/record/${recordId}/details`,
      method: 'GET'
    })
  }

  /**
   * 获取资产盘点历史
   * @param {string} assetId 资产ID
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getAssetHistory(assetId, params = {}) {
    return request({
      url: `/asset/stocktaking/asset/${assetId}/history`,
      method: 'GET',
      data: params
    })
  }

  // ==================== 语音转文字相关 ====================

  /**
   * 语音转文字
   * @param {string} audioUrl 语音文件URL
   * @returns {Promise}
   */
  static speechToText(audioUrl) {
    return request({
      url: '/common/speech-to-text',
      method: 'POST',
      data: { audioUrl }
    })
  }
}
