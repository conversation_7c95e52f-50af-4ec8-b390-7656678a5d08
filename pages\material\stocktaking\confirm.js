// pages/material/stocktaking/confirm.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { StocktakingAPI } from '../../../utils/stocktaking-api.js'

Page({
  data: {
    // 任务ID和资产ID
    taskId: '',
    assetId: '',
    fromScan: false,
    
    // 资产信息
    assetInfo: {},
    
    // 确认数据
    confirmData: {
      actualLocation: '',
      actualStatus: '',
      foundStatus: 'found',
      differenceReason: '',
      photos: [],
      voiceUrl: '',
      remark: ''
    },
    
    // 状态选择器
    showStatusPicker: false,
    statusActions: [
      { name: '正常使用', value: 'normal' },
      { name: '闲置', value: 'idle' },
      { name: '维修中', value: 'repairing' },
      { name: '报废', value: 'scrapped' },
      { name: '丢失', value: 'lost' }
    ],
    
    // 差异类型选择器
    showDifferenceTypePicker: false,
    differenceTypeActions: [
      { name: '盘盈', value: 'surplus' },
      { name: '盘亏', value: 'deficit' },
      { name: '状态差异', value: 'status_diff' },
      { name: '位置差异', value: 'location_diff' },
      { name: '其他差异', value: 'other' }
    ],
    
    // 计算属性
    hasDifference: false,
    differenceType: ''
  },

  onLoad(options) {
    console.log('📋 资产确认页面加载', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取参数
    const { taskId, assetId, fromScan } = options
    if (!taskId || !assetId) {
      Toast.fail('参数不完整')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ 
      taskId, 
      assetId,
      fromScan: fromScan === 'true'
    })

    // 加载资产信息
    this.loadAssetInfo()
  },

  /**
   * 加载资产信息
   */
  async loadAssetInfo() {
    try {
      // 调用真实API
      const response = await StocktakingAPI.getAssetInfo(this.data.assetId)
      
      if (response && response.code === 200 && response.data) {
        const assetInfo = response.data
        
        this.setData({
          assetInfo,
          'confirmData.actualLocation': assetInfo.bookLocation,
          'confirmData.actualStatus': assetInfo.bookStatus
        })
        
        // 检查是否有差异
        this.checkDifference()
      } else {
        throw new Error(response?.msg || '获取资产信息失败')
      }
    } catch (error) {
      console.error('❌ 加载资产信息失败:', error)
      Toast.fail('获取资产信息失败')
    }
  },



  /**
   * 实际位置变化
   */
  onActualLocationChange(event) {
    this.setData({
      'confirmData.actualLocation': event.detail
    })
    this.checkDifference()
  },

  /**
   * 选择实际状态
   */
  onSelectStatus() {
    this.setData({ showStatusPicker: true })
  },

  /**
   * 关闭状态选择器
   */
  onCloseStatusPicker() {
    this.setData({ showStatusPicker: false })
  },

  /**
   * 选择状态动作
   */
  onSelectStatusAction(event) {
    const status = event.detail.value
    this.setData({
      'confirmData.actualStatus': status,
      showStatusPicker: false
    })
    this.checkDifference()
  },

  /**
   * 发现状态变化
   */
  onFoundStatusChange(event) {
    this.setData({
      'confirmData.foundStatus': event.detail
    })
    this.checkDifference()
  },

  /**
   * 检查差异
   */
  checkDifference() {
    const { assetInfo, confirmData } = this.data
    
    let hasDifference = false
    let differenceType = ''
    
    // 检查位置差异
    if (confirmData.actualLocation !== assetInfo.bookLocation) {
      hasDifference = true
      differenceType = '位置差异'
    }
    
    // 检查状态差异
    if (confirmData.actualStatus !== assetInfo.bookStatus) {
      hasDifference = true
      differenceType = differenceType ? '多项差异' : '状态差异'
    }
    
    // 检查是否未找到
    if (confirmData.foundStatus === 'not_found') {
      hasDifference = true
      differenceType = '盘亏'
    }
    
    this.setData({
      hasDifference,
      differenceType
    })
  },

  /**
   * 选择差异类型
   */
  onSelectDifferenceType() {
    this.setData({ showDifferenceTypePicker: true })
  },

  /**
   * 关闭差异类型选择器
   */
  onCloseDifferenceTypePicker() {
    this.setData({ showDifferenceTypePicker: false })
  },

  /**
   * 选择差异类型动作
   */
  onSelectDifferenceTypeAction(event) {
    const type = event.detail.name
    this.setData({
      differenceType: type,
      showDifferenceTypePicker: false
    })
  },

  /**
   * 差异原因变化
   */
  onDifferenceReasonChange(event) {
    this.setData({
      'confirmData.differenceReason': event.detail
    })
  },

  /**
   * 拍照
   */
  onTakePhoto() {
    console.log('📷 拍照记录')

    wx.chooseMedia({
      count: 3 - this.data.confirmData.photos.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        console.log('选择图片成功:', res)
        this.uploadPhotos(res.tempFiles)
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        if (error.errMsg && !error.errMsg.includes('cancel')) {
          Toast.fail('选择图片失败')
        }
      }
    })
  },

  /**
   * 上传照片
   */
  async uploadPhotos(tempFiles) {
    Toast.loading('上传中...')

    try {
      const uploadPromises = tempFiles.map(file => this.uploadSinglePhoto(file.tempFilePath))
      const uploadResults = await Promise.all(uploadPromises)

      const newPhotos = [...this.data.confirmData.photos, ...uploadResults]
      this.setData({
        'confirmData.photos': newPhotos
      })

      Toast.clear()
      Toast.success('上传成功')
    } catch (error) {
      console.error('上传照片失败:', error)
      Toast.clear()
      Toast.fail('上传失败')
    }
  },

  /**
   * 上传单张照片
   */
  uploadSinglePhoto(filePath) {
    return StocktakingAPI.uploadPhoto(filePath, {
      type: 'stocktaking',
      taskId: this.data.taskId,
      assetId: this.data.assetId
    }).then(response => {
      return response.data.url // 返回服务器URL
    })
  },

  /**
   * 预览照片
   */
  onPreviewPhoto(event) {
    const index = event.currentTarget.dataset.index
    const photos = this.data.confirmData.photos

    wx.previewImage({
      current: photos[index],
      urls: photos
    })
  },

  /**
   * 删除照片
   */
  onDeletePhoto(event) {
    const index = event.currentTarget.dataset.index
    const photos = [...this.data.confirmData.photos]
    photos.splice(index, 1)

    this.setData({
      'confirmData.photos': photos
    })
  },

  /**
   * 录制语音
   */
  onRecordVoice() {
    console.log('🎤 录制语音')

    wx.showModal({
      title: '语音录制',
      content: '语音录制功能开发中，敬请期待',
      showCancel: false
    })
  },

  /**
   * 播放语音
   */
  onPlayVoice() {
    console.log('🔊 播放语音')

    wx.showModal({
      title: '语音播放',
      content: '语音播放功能开发中，敬请期待',
      showCancel: false
    })
  },

  /**
   * 备注变化
   */
  onRemarkChange(event) {
    this.setData({
      'confirmData.remark': event.detail
    })
  },

  /**
   * 取消
   */
  onCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消当前操作吗？已填写的信息将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  /**
   * 确认提交
   */
  async onConfirm() {
    // 验证必填项
    if (!this.validateForm()) {
      return
    }

    try {
      Toast.loading('提交中...')

      // 构建提交数据
      const submitData = {
        taskId: this.data.taskId,
        assetId: this.data.assetId,
        actualLocation: this.data.confirmData.actualLocation,
        actualStatus: this.data.confirmData.actualStatus,
        foundStatus: this.data.confirmData.foundStatus,
        hasDifference: this.data.hasDifference,
        differenceType: this.data.differenceType,
        differenceReason: this.data.confirmData.differenceReason,
        photos: this.data.confirmData.photos,
        voiceUrl: this.data.confirmData.voiceUrl,
        remark: this.data.confirmData.remark
      }

      console.log('💾 提交盘点确认:', submitData)

      // 调用真实API创建盘点记录
      const response = await StocktakingAPI.createRecord(submitData)

      if (response && response.code === 200) {
        Toast.clear()
        Toast.success('提交成功')

        // 延迟返回
        setTimeout(() => {
          if (this.data.fromScan) {
            // 从扫码页面来的，返回扫码页面
            wx.navigateBack()
          } else {
            // 从其他页面来的，返回任务详情页面
            wx.navigateBack()
          }
        }, 1500)
      } else {
        throw new Error(response?.msg || '提交失败')
      }
    } catch (error) {
      console.error('❌ 提交盘点确认失败:', error)
      Toast.clear()
      Toast.fail(error.message || '提交失败')
    }
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { confirmData, hasDifference } = this.data

    // 检查实际位置
    if (!confirmData.actualLocation.trim()) {
      Toast.fail('请输入实际位置')
      return false
    }

    // 检查实际状态
    if (!confirmData.actualStatus) {
      Toast.fail('请选择实际状态')
      return false
    }

    // 如果有差异，检查差异原因
    if (hasDifference && !confirmData.differenceReason.trim()) {
      Toast.fail('存在差异时请填写差异原因')
      return false
    }

    return true
  },


})
