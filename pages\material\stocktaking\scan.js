// pages/material/stocktaking/scan.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { StocktakingAPI } from '../../../utils/stocktaking-api.js'

Page({
  data: {
    // 任务ID
    taskId: '',
    
    // 任务信息
    taskInfo: {},
    
    // 最近扫码记录
    recentScans: [],
    
    // 手动输入弹窗
    showManualDialog: false,
    manualCode: '',
    
    // 加载状态
    loading: false
  },

  onLoad(options) {
    console.log('📷 扫码盘点页面加载', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取任务ID
    const { taskId } = options
    if (!taskId) {
      Toast.fail('任务ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ taskId })

    // 加载任务信息
    this.loadTaskInfo()
    
    // 加载最近扫码记录
    this.loadRecentScans()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.taskId) {
      this.loadTaskInfo()
      this.loadRecentScans()
    }
  },

  /**
   * 加载任务信息
   */
  async loadTaskInfo() {
    try {
      // 调用真实API
      const response = await StocktakingAPI.getTaskDetail(this.data.taskId)
      
      if (response && response.code === 200 && response.data) {
        this.setData({
          taskInfo: response.data
        })
      } else {
        throw new Error(response?.msg || '获取任务信息失败')
      }
    } catch (error) {
      console.error('❌ 加载任务信息失败:', error)
      Toast.fail('获取任务信息失败')
    }
  },

  /**
   * 加载最近扫码记录
   */
  async loadRecentScans() {
    try {
      // 从本地存储获取最近扫码记录
      const recentScans = wx.getStorageSync('recent_scans') || []
      this.setData({
        recentScans: recentScans.slice(0, 10) // 最多显示10条
      })
    } catch (error) {
      console.error('❌ 加载扫码记录失败:', error)
      // 扫码记录失败不显示错误提示，只在控制台记录
    }
  },

  /**
   * 扫码
   */
  onScanCode() {
    console.log('📷 开始扫码')

    wx.scanCode({
      scanType: ['barCode', 'qrCode'],
      success: (res) => {
        console.log('扫码结果:', res.result)
        this.handleScanResult(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        if (error.errMsg && error.errMsg.includes('cancel')) {
          // 用户取消扫码，不显示错误提示
          return
        }
        Toast.fail('扫码失败，请重试')
      }
    })
  },

  /**
   * 手动输入
   */
  onManualInput() {
    console.log('⌨️ 手动输入资产编码')
    this.setData({
      showManualDialog: true,
      manualCode: ''
    })
  },

  /**
   * 手动编码输入
   */
  onManualCodeChange(event) {
    this.setData({
      manualCode: event.detail
    })
  },

  /**
   * 确认手动输入
   */
  onConfirmManual() {
    const code = this.data.manualCode.trim()
    if (!code) {
      Toast.fail('请输入资产编码')
      return
    }

    this.setData({ showManualDialog: false })
    this.handleScanResult(code)
  },

  /**
   * 取消手动输入
   */
  onCancelManual() {
    this.setData({
      showManualDialog: false,
      manualCode: ''
    })
  },

  /**
   * 处理扫码结果
   */
  async handleScanResult(code) {
    console.log('🔍 处理扫码结果:', code)
    
    this.setData({ loading: true })

    try {
      // 调用真实API查找资产
      const response = await StocktakingAPI.getAssetInfo(code)
      
      if (response && response.code === 200 && response.data) {
        const asset = response.data
        
        // 跳转到资产确认页面
        wx.navigateTo({
          url: `/pages/material/stocktaking/confirm?taskId=${this.data.taskId}&assetId=${asset.assetId}&fromScan=true`,
          success: () => {
            // 添加到最近扫码记录
            this.addRecentScan(asset, 'success')
          },
          fail: () => {
            Toast.fail('资产确认功能开发中')
          }
        })
      } else {
        // 未找到资产
        Toast.fail('未找到对应的资产，请检查编码是否正确')
        
        // 添加到最近扫码记录（失败）
        this.addRecentScan({
          assetCode: code,
          assetName: '未知资产'
        }, 'failed')
      }
    } catch (error) {
      console.error('❌ 查找资产失败:', error)
      Toast.fail('查找资产失败，请重试')
      
      // 添加到最近扫码记录（失败）
      this.addRecentScan({
        assetCode: code,
        assetName: '查找失败'
      }, 'failed')
    } finally {
      this.setData({ loading: false })
    }
  },



  /**
   * 添加最近扫码记录
   */
  addRecentScan(asset, status) {
    const newScan = {
      scanId: 'scan_' + Date.now(),
      assetId: asset.assetId || '',
      assetCode: asset.assetCode,
      assetName: asset.assetName,
      status: status,
      scanTime: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '-')
    }

    // 添加到列表头部，最多保留10条记录
    const recentScans = [newScan, ...this.data.recentScans].slice(0, 10)

    this.setData({ recentScans })

    // 保存到本地存储
    try {
      wx.setStorageSync('recent_scans', recentScans)
    } catch (error) {
      console.error('保存扫码记录失败:', error)
    }
  },

  /**
   * 最近记录项点击
   */
  onRecentItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击最近记录:', item)
    
    if (item.status === 'success' && item.assetId) {
      // 跳转到资产确认页面
      wx.navigateTo({
        url: `/pages/material/stocktaking/confirm?taskId=${this.data.taskId}&assetId=${item.assetId}`,
        fail: () => {
          Toast.fail('资产确认功能开发中')
        }
      })
    } else {
      // 重新扫码或输入
      Toast('该记录无效，请重新扫码')
    }
  },

  /**
   * 查看任务详情
   */
  onViewTaskDetail() {
    wx.navigateTo({
      url: `/pages/material/stocktaking/task/detail?taskId=${this.data.taskId}`,
      fail: () => {
        Toast.fail('任务详情功能开发中')
      }
    })
  },

  /**
   * 查看进度
   */
  onViewProgress() {
    wx.navigateTo({
      url: `/pages/material/stocktaking/progress?taskId=${this.data.taskId}`,
      fail: () => {
        Toast.fail('进度查看功能开发中')
      }
    })
  }
})
